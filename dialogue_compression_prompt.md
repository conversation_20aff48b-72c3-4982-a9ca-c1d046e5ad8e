# AI对话压缩提示词

## 任务目标
将完整的AI对话记录压缩为简化版本，大幅减少token消耗的同时保持核心信息和逻辑完整性。

## 压缩原则

### 1. 保持对话形式
- 输出仍然是User和Assistant的对话格式
- 不要写成"用户表示..."、"AI确认..."这种描述性语言
- 让压缩后的内容看起来像两个人在自然聊天

### 2. 信息自包含化
- 每个发言都要能独立理解，不依赖上下文
- 用户的简短回答要补充完整背景信息
- AI的发言要保留核心教学内容和推进逻辑

### 3. 去除冗余表达
- 删除重复的礼貌用语、过渡词汇
- 合并意思相近的多个句子
- 保留关键概念、比喻、例子，删除详细解释

### 4. 突出关键节点
- 保留用户的理解突破时刻
- 保留重要概念的首次出现
- 保留逻辑推进的关键转折点

## 具体操作步骤

### 用户发言处理
- **简短回答**：补充上下文变成完整表达
  - 原文："变大了" 
  - 压缩："所以运动让海马体变大了"
- **长回答**：提炼核心观点
  - 保留主要意图和关键信息
  - 删除重复和冗余部分

### AI发言处理
- **教学内容**：保留核心概念和关键比喻
  - 保留："BDNF'大脑肥料'"、"多巴胺'啦啦队'"等形象比喻
  - 删除：详细的科学解释和重复强调
- **引导提问**：简化为直接问题
  - 原文："那问题就来了，怎么才能让你的HPA轴不那么大惊小怪..."
  - 压缩："长期压力下你觉得哪个先磨损？"

## 质量检查标准

### 必须保留的元素
1. **核心概念**：所有关键术语和比喻
2. **逻辑链条**：学习内容的推进顺序
3. **互动模式**：提问-回答-确认的教学节奏
4. **突破时刻**：用户理解和顿悟的关键点

### 可以删除的元素
1. **冗余解释**：重复说明同一概念
2. **过渡语言**："好的"、"没错"、"那么"等
3. **详细举例**：保留核心例子，删除展开说明
4. **情感表达**：过多的赞美和鼓励词汇

## 示例对比

### 压缩前（冗余版本）
```
User: 我要入门这个
Assistant: 没问题！这本《大脑健身房》听起来就很有意思。咱们不着急一下子把所有东西都看完，那会像吃自助餐一样，看着都好，但吃撑了反而难受。我们就从一个好玩的问题开始吧。咱们聊到健身，你一般会想到什么？是为了身材，还是为了健康？
```

### 压缩后（目标版本）
```
User: 想要入门学习《大脑健身房》这本书
Assistant: 咱们从一个问题开始吧，你健身一般是为了什么？
```

## 执行指令
请按照以上原则，将提供的完整对话记录压缩为简化版本。确保：
- 保持自然对话风格
- 每个发言都自包含
- 核心学习内容完整
- token消耗大幅减少（目标：减少70-80%）
