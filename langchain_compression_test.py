#!/usr/bin/env python3
"""
AI对话压缩测试脚本 - LangChain方案
基于LangChain的ConversationSummaryBufferMemory实现对话压缩
"""

import os
import re
from typing import List, Tuple, Dict
from langchain.memory import ConversationSummaryBufferMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class DialogueCompressor:
    def __init__(self, api_key: str, base_url: str = None, model_name: str = "gpt-4o-mini"):
        """初始化对话压缩器"""
        llm_kwargs = {
            "api_key": api_key,
            "model_name": model_name,
            "temperature": 0.1
        }

        if base_url:
            llm_kwargs["base_url"] = base_url

        self.llm = ChatOpenAI(**llm_kwargs)
        
        # 自定义压缩提示词
        self.compression_prompt = PromptTemplate(
            input_variables=["summary", "new_lines"],
            template="""
你是一个专业的对话压缩专家。请将以下对话内容压缩为简洁但完整的版本。

压缩原则：
1. 保持自然对话风格，不要用"用户表示"、"AI确认"等描述性语言
2. 每个发言都要自包含，能独立理解
3. 用户的简短回答要补充上下文背景
4. 保留核心概念、关键比喻和学习突破时刻
5. 删除冗余的礼貌用语和重复解释

当前摘要: {summary}

新的对话内容:
{new_lines}

请输出压缩后的对话，保持User/Assistant格式：
"""
        )
    
    def parse_dialogue_file(self, file_path: str) -> List[Tuple[str, str]]:
        """解析对话文件，返回(role, content)列表"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析markdown格式的对话
        messages = []
        current_role = None
        current_content = []
        
        lines = content.split('\n')
        for line in lines:
            if line.startswith('## 🧑‍💻 User'):
                if current_role and current_content:
                    messages.append((current_role, '\n'.join(current_content).strip()))
                current_role = 'User'
                current_content = []
            elif line.startswith('## 🤖 Assistant'):
                if current_role and current_content:
                    messages.append((current_role, '\n'.join(current_content).strip()))
                current_role = 'Assistant'
                current_content = []
            elif line.strip() and not line.startswith('#'):
                current_content.append(line)
        
        # 添加最后一条消息
        if current_role and current_content:
            messages.append((current_role, '\n'.join(current_content).strip()))
        
        return messages
    
    def compress_dialogue_langchain(self, messages: List[Tuple[str, str]], max_token_limit: int = 1000) -> str:
        """使用LangChain的ConversationSummaryBufferMemory压缩对话"""
        
        # 创建内存对象
        memory = ConversationSummaryBufferMemory(
            llm=self.llm,
            max_token_limit=max_token_limit,
            return_messages=True
        )
        
        # 添加消息到内存
        for role, content in messages:
            if role == 'User':
                memory.chat_memory.add_user_message(content)
            else:
                memory.chat_memory.add_ai_message(content)
        
        # 获取压缩后的消息
        compressed_messages = memory.chat_memory.messages
        
        # 格式化输出
        result = []
        for msg in compressed_messages:
            if isinstance(msg, HumanMessage):
                result.append(f"## 🧑‍💻 User\n{msg.content}\n")
            elif isinstance(msg, AIMessage):
                result.append(f"## 🤖 Assistant\n{msg.content}\n")
        
        return '\n'.join(result)
    
    def compress_dialogue_custom(self, messages: List[Tuple[str, str]], chunk_size: int = 6) -> str:
        """使用自定义方法压缩对话"""
        
        if len(messages) <= chunk_size:
            # 如果消息不多，直接返回
            result = []
            for role, content in messages:
                result.append(f"## 🧑‍💻 {role}\n{content}\n")
            return '\n'.join(result)
        
        compressed_parts = []
        summary = ""
        
        # 分块处理
        for i in range(0, len(messages), chunk_size):
            chunk = messages[i:i + chunk_size]
            
            # 格式化当前块
            chunk_text = []
            for role, content in chunk:
                chunk_text.append(f"{role}: {content}")
            
            # 使用LLM压缩当前块
            prompt = self.compression_prompt.format(
                summary=summary,
                new_lines='\n'.join(chunk_text)
            )
            
            response = self.llm.invoke(prompt)
            compressed_chunk = response.content
            
            compressed_parts.append(compressed_chunk)
            
            # 更新摘要（简化版本）
            if len(compressed_parts) > 1:
                summary = f"前面的对话涉及了{len(compressed_parts)-1}个主要话题"
        
        return '\n---\n'.join(compressed_parts)
    
    def calculate_compression_ratio(self, original: str, compressed: str) -> Dict[str, float]:
        """计算压缩比率"""
        original_chars = len(original)
        compressed_chars = len(compressed)
        
        # 简单的token估算（1 token ≈ 4 chars for Chinese）
        original_tokens = original_chars / 4
        compressed_tokens = compressed_chars / 4
        
        return {
            'char_ratio': compressed_chars / original_chars,
            'token_ratio': compressed_tokens / original_tokens,
            'char_reduction': (original_chars - compressed_chars) / original_chars,
            'token_reduction': (original_tokens - compressed_tokens) / original_tokens
        }

def main():
    """主测试函数"""

    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("请设置OPENAI_API_KEY环境变量")
        return

    # 获取base_url（可选）
    base_url = os.getenv('OPENAI_BASE_URL')

    # 初始化压缩器
    compressor = DialogueCompressor(api_key, base_url)
    
    # 测试文件路径
    input_file = "real_dialogues/大脑健身房_chatlog.md"
    
    if not os.path.exists(input_file):
        print(f"测试文件不存在: {input_file}")
        return
    
    print("🚀 开始对话压缩测试...")
    
    # 解析原始对话
    print("📖 解析原始对话文件...")
    messages = compressor.parse_dialogue_file(input_file)
    print(f"解析到 {len(messages)} 条消息")
    
    # 读取原始文件内容用于计算压缩比
    with open(input_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # 方法1：LangChain ConversationSummaryBufferMemory
    print("\n🔧 测试方法1: LangChain ConversationSummaryBufferMemory")
    try:
        compressed_langchain = compressor.compress_dialogue_langchain(messages, max_token_limit=800)
        
        # 保存结果
        with open("compressed_langchain_result.md", 'w', encoding='utf-8') as f:
            f.write("# 大脑健身入门指导 - LangChain压缩版\n\n")
            f.write(compressed_langchain)
        
        # 计算压缩比
        ratio_langchain = compressor.calculate_compression_ratio(original_content, compressed_langchain)
        print(f"✅ LangChain方法完成")
        print(f"   字符压缩比: {ratio_langchain['char_ratio']:.2%}")
        print(f"   Token减少: {ratio_langchain['token_reduction']:.2%}")
        
    except Exception as e:
        print(f"❌ LangChain方法失败: {e}")
    
    # 方法2：自定义压缩方法
    print("\n🔧 测试方法2: 自定义压缩方法")
    try:
        compressed_custom = compressor.compress_dialogue_custom(messages, chunk_size=4)
        
        # 保存结果
        with open("compressed_custom_result.md", 'w', encoding='utf-8') as f:
            f.write("# 大脑健身入门指导 - 自定义压缩版\n\n")
            f.write(compressed_custom)
        
        # 计算压缩比
        ratio_custom = compressor.calculate_compression_ratio(original_content, compressed_custom)
        print(f"✅ 自定义方法完成")
        print(f"   字符压缩比: {ratio_custom['char_ratio']:.2%}")
        print(f"   Token减少: {ratio_custom['token_reduction']:.2%}")
        
    except Exception as e:
        print(f"❌ 自定义方法失败: {e}")
    
    print("\n📊 测试完成！请查看生成的压缩文件并与人工压缩版本对比。")

if __name__ == "__main__":
    main()
