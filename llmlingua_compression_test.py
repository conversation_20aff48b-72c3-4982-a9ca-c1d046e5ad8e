#!/usr/bin/env python3
"""
基于LLMLingua的AI对话压缩测试
使用Microsoft的LLMLingua进行专业的prompt压缩
"""

import os
import json
from typing import List, Tuple, Dict
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class LLMLinguaDialogueCompressor:
    def __init__(self):
        """初始化LLMLingua压缩器"""
        try:
            from llmlingua import PromptCompressor
            
            # 初始化LLMLingua压缩器
            self.compressor = PromptCompressor()
            print("✅ LLMLingua初始化成功")
            
        except ImportError:
            print("❌ LLMLingua未安装，请运行: pip install llmlingua")
            raise
    
    def parse_dialogue_file(self, file_path: str) -> str:
        """解析对话文件，返回适合LLMLingua的格式"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 将markdown格式转换为LLMLingua的结构化格式
        lines = content.split('\n')
        structured_content = []
        current_speaker = None
        current_content = []
        
        for line in lines:
            if line.startswith('## 🧑‍💻 User'):
                if current_speaker and current_content:
                    # 添加LLMLingua标签，用户发言压缩率稍低
                    structured_content.append(f'<llmlingua, compress=False>User:</llmlingua>')
                    structured_content.append(f'<llmlingua, rate=0.3>{" ".join(current_content).strip()}</llmlingua>')
                current_speaker = 'User'
                current_content = []
            elif line.startswith('## 🤖 Assistant'):
                if current_speaker and current_content:
                    if current_speaker == 'User':
                        structured_content.append(f'<llmlingua, compress=False>User:</llmlingua>')
                        structured_content.append(f'<llmlingua, rate=0.3>{" ".join(current_content).strip()}</llmlingua>')
                    else:
                        structured_content.append(f'<llmlingua, compress=False>Assistant:</llmlingua>')
                        structured_content.append(f'<llmlingua, rate=0.5>{" ".join(current_content).strip()}</llmlingua>')
                current_speaker = 'Assistant'
                current_content = []
            elif line.strip() and not line.startswith('#'):
                current_content.append(line.strip())
        
        # 添加最后一条消息
        if current_speaker and current_content:
            if current_speaker == 'User':
                structured_content.append(f'<llmlingua, compress=False>User:</llmlingua>')
                structured_content.append(f'<llmlingua, rate=0.3>{" ".join(current_content).strip()}</llmlingua>')
            else:
                structured_content.append(f'<llmlingua, compress=False>Assistant:</llmlingua>')
                structured_content.append(f'<llmlingua, rate=0.5>{" ".join(current_content).strip()}</llmlingua>')
        
        return '\n'.join(structured_content)
    
    def compress_dialogue_basic(self, content: str, target_ratio: float = 0.3) -> Dict:
        """基础压缩方法"""
        try:
            compressed_result = self.compressor.compress_prompt(
                content,
                instruction="",
                question="",
                ratio=target_ratio
            )
            return compressed_result
        except Exception as e:
            print(f"基础压缩失败: {e}")
            return None
    
    def compress_dialogue_structured(self, structured_content: str) -> Dict:
        """结构化压缩方法（使用LLMLingua标签）"""
        try:
            compressed_result = self.compressor.structured_compress_prompt(
                structured_content,
                instruction="",
                question="",
                rate=0.4  # 全局压缩率
            )
            return compressed_result
        except Exception as e:
            print(f"结构化压缩失败: {e}")
            return None
    
    def compress_dialogue_advanced(self, content: str, target_tokens: int = 800) -> Dict:
        """高级压缩方法（使用LongLLMLingua参数）"""
        try:
            # 将内容按行分割，适合LongLLMLingua处理
            content_lines = content.split('\n')
            
            compressed_result = self.compressor.compress_prompt(
                content_lines,
                instruction="",
                question="",
                target_token=target_tokens,
                # LongLLMLingua特殊参数
                condition_in_question="after_condition",
                reorder_context="sort",
                dynamic_context_compression_ratio=0.3,
                condition_compare=True,
                context_budget="+100",
                rank_method="longllmlingua"
            )
            return compressed_result
        except Exception as e:
            print(f"高级压缩失败: {e}")
            return None
    
    def format_compressed_result(self, compressed_result: Dict, method_name: str) -> str:
        """格式化压缩结果为可读的对话格式"""
        if not compressed_result:
            return "压缩失败"
        
        compressed_text = compressed_result.get('compressed_prompt', '')
        
        # 尝试恢复对话格式
        formatted_lines = []
        lines = compressed_text.split('\n')
        
        current_speaker = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('User:'):
                current_speaker = 'User'
                formatted_lines.append(f"\n## 🧑‍💻 {current_speaker}")
                content = line.replace('User:', '').strip()
                if content:
                    formatted_lines.append(content)
            elif line.startswith('Assistant:'):
                current_speaker = 'Assistant'
                formatted_lines.append(f"\n## 🤖 {current_speaker}")
                content = line.replace('Assistant:', '').strip()
                if content:
                    formatted_lines.append(content)
            else:
                if current_speaker:
                    formatted_lines.append(line)
                else:
                    # 如果没有明确的speaker，尝试推断
                    formatted_lines.append(line)
        
        result = f"# 大脑健身入门指导 - LLMLingua压缩版 ({method_name})\n"
        result += '\n'.join(formatted_lines)
        
        return result
    
    def calculate_compression_stats(self, original: str, compressed_result: Dict) -> Dict:
        """计算压缩统计信息"""
        if not compressed_result:
            return {}
        
        original_chars = len(original)
        compressed_chars = len(compressed_result.get('compressed_prompt', ''))
        
        # 从LLMLingua结果中获取token信息
        original_tokens = compressed_result.get('origin_tokens', original_chars // 4)
        compressed_tokens = compressed_result.get('compressed_tokens', compressed_chars // 4)
        
        return {
            'original_chars': original_chars,
            'compressed_chars': compressed_chars,
            'original_tokens': original_tokens,
            'compressed_tokens': compressed_tokens,
            'char_ratio': compressed_chars / original_chars if original_chars > 0 else 0,
            'token_ratio': compressed_tokens / original_tokens if original_tokens > 0 else 0,
            'char_reduction': (original_chars - compressed_chars) / original_chars if original_chars > 0 else 0,
            'token_reduction': (original_tokens - compressed_tokens) / original_tokens if original_tokens > 0 else 0,
            'compression_ratio': compressed_result.get('ratio', 'N/A'),
            'saving_info': compressed_result.get('saving', 'N/A')
        }

def main():
    """主测试函数"""
    print("🚀 LLMLingua对话压缩测试")
    print("=" * 40)
    
    # 初始化压缩器
    try:
        compressor = LLMLinguaDialogueCompressor()
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    
    # 测试文件路径
    input_file = "real_dialogues/大脑健身房_chatlog.md"
    
    if not os.path.exists(input_file):
        print(f"测试文件不存在: {input_file}")
        return
    
    # 读取原始文件
    with open(input_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print(f"📖 原始文件: {len(original_content)} 字符")
    
    # 方法1: 基础压缩
    print("\n🔧 测试方法1: 基础压缩 (ratio=0.3)")
    basic_result = compressor.compress_dialogue_basic(original_content, target_ratio=0.3)
    
    if basic_result:
        formatted_basic = compressor.format_compressed_result(basic_result, "基础压缩")
        with open("llmlingua_basic_result.md", 'w', encoding='utf-8') as f:
            f.write(formatted_basic)
        
        stats_basic = compressor.calculate_compression_stats(original_content, basic_result)
        print(f"✅ 基础压缩完成")
        print(f"   Token压缩比: {stats_basic.get('token_ratio', 0):.2%}")
        print(f"   Token减少: {stats_basic.get('token_reduction', 0):.2%}")
        print(f"   压缩倍数: {stats_basic.get('compression_ratio', 'N/A')}")
    
    # 方法2: 结构化压缩
    print("\n🔧 测试方法2: 结构化压缩 (使用LLMLingua标签)")
    structured_content = compressor.parse_dialogue_file(input_file)
    structured_result = compressor.compress_dialogue_structured(structured_content)
    
    if structured_result:
        formatted_structured = compressor.format_compressed_result(structured_result, "结构化压缩")
        with open("llmlingua_structured_result.md", 'w', encoding='utf-8') as f:
            f.write(formatted_structured)
        
        stats_structured = compressor.calculate_compression_stats(original_content, structured_result)
        print(f"✅ 结构化压缩完成")
        print(f"   Token压缩比: {stats_structured.get('token_ratio', 0):.2%}")
        print(f"   Token减少: {stats_structured.get('token_reduction', 0):.2%}")
        print(f"   压缩倍数: {stats_structured.get('compression_ratio', 'N/A')}")
    
    # 方法3: 高级压缩
    print("\n🔧 测试方法3: 高级压缩 (LongLLMLingua, target=800 tokens)")
    advanced_result = compressor.compress_dialogue_advanced(original_content, target_tokens=800)
    
    if advanced_result:
        formatted_advanced = compressor.format_compressed_result(advanced_result, "高级压缩")
        with open("llmlingua_advanced_result.md", 'w', encoding='utf-8') as f:
            f.write(formatted_advanced)
        
        stats_advanced = compressor.calculate_compression_stats(original_content, advanced_result)
        print(f"✅ 高级压缩完成")
        print(f"   Token压缩比: {stats_advanced.get('token_ratio', 0):.2%}")
        print(f"   Token减少: {stats_advanced.get('token_reduction', 0):.2%}")
        print(f"   压缩倍数: {stats_advanced.get('compression_ratio', 'N/A')}")
    
    print("\n📊 LLMLingua测试完成！")
    print("📁 生成的文件:")
    print("   - llmlingua_basic_result.md")
    print("   - llmlingua_structured_result.md") 
    print("   - llmlingua_advanced_result.md")
    print("\n💡 请对比这些结果与之前的压缩方法效果")

if __name__ == "__main__":
    main()
