#!/usr/bin/env python3
"""
快速测试脚本 - 简化版本
用于快速验证对话压缩效果
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_requirements():
    """检查运行环境"""
    try:
        import langchain
        from langchain_openai import ChatOpenAI
        print("✅ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install langchain langchain-openai python-dotenv")
        return False

def simple_compression_test():
    """简单的压缩测试"""
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        return False
    
    # 检查测试文件
    test_file = "real_dialogues/大脑健身房_chatlog.md"
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    try:
        from langchain_openai import ChatOpenAI

        # 获取base_url（可选）
        base_url = os.getenv('OPENAI_BASE_URL')

        # 初始化模型
        llm_kwargs = {
            "api_key": api_key,
            "model_name": "gpt-4o-mini",
            "temperature": 0.1
        }

        if base_url:
            llm_kwargs["base_url"] = base_url

        llm = ChatOpenAI(**llm_kwargs)
        
        # 读取测试文件
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的压缩提示词
        compression_prompt = f"""
请将以下对话压缩为简洁版本，要求：
1. 保持User/Assistant对话格式
2. 每个发言都要自包含，能独立理解
3. 用户简短回答要补充上下文
4. 保留核心概念和学习进展
5. 删除冗余表达，目标压缩70%以上

原始对话：
{content[:3000]}...

请输出压缩后的对话：
"""
        
        print("🔧 开始压缩测试...")
        response = llm.invoke(compression_prompt)
        compressed_result = response.content
        
        # 保存结果
        output_file = "quick_test_result.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 快速测试压缩结果\n\n")
            f.write(compressed_result)
        
        # 计算压缩比
        original_chars = len(content)
        compressed_chars = len(compressed_result)
        compression_ratio = compressed_chars / original_chars
        
        print(f"✅ 压缩完成！")
        print(f"📊 压缩统计:")
        print(f"   原始字符数: {original_chars}")
        print(f"   压缩后字符数: {compressed_chars}")
        print(f"   压缩比: {compression_ratio:.2%}")
        print(f"   减少: {(1-compression_ratio):.2%}")
        print(f"📁 结果保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 压缩测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AI对话压缩 - 快速测试")
    print("=" * 30)
    
    # 检查环境
    if not check_requirements():
        sys.exit(1)
    
    # 运行测试
    if simple_compression_test():
        print("\n🎉 测试成功完成！")
        print("💡 接下来可以:")
        print("   1. 查看 quick_test_result.md 的压缩效果")
        print("   2. 与 compressed_dialogue_v2.md 对比质量")
        print("   3. 运行完整测试: python langchain_compression_test.py")
    else:
        print("\n❌ 测试失败，请检查环境配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
