#!/bin/bash

# AI对话压缩测试 - 快速启动脚本

echo "🚀 AI对话Token优化项目 - 快速测试"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📥 安装依赖包..."
pip install --upgrade pip
pip install langchain langchain-openai python-dotenv

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  请设置OPENAI_API_KEY环境变量"
    echo "   export OPENAI_API_KEY='your-api-key-here'"
    echo "   或者创建.env文件包含: OPENAI_API_KEY=your-api-key-here"
    
    # 检查是否有.env文件
    if [ -f ".env" ]; then
        echo "📄 发现.env文件，加载环境变量..."
        export $(cat .env | xargs)
    else
        echo "❌ 未找到API密钥，请设置后重新运行"
        exit 1
    fi
fi

# 检查测试文件
if [ ! -f "real_dialogues/大脑健身房_chatlog.md" ]; then
    echo "❌ 测试文件不存在: real_dialogues/大脑健身房_chatlog.md"
    echo "   请确保测试数据文件存在"
    exit 1
fi

# 运行测试
echo "🏃 开始运行压缩测试..."
python3 langchain_compression_test.py

echo ""
echo "✅ 测试完成！"
echo "📁 生成的文件："
echo "   - compressed_langchain_result.md (LangChain方法结果)"
echo "   - compressed_custom_result.md (自定义方法结果)"
echo ""
echo "🔍 请对比以下文件的质量："
echo "   - compressed_dialogue_v2.md (人工压缩基准)"
echo "   - compressed_by_prompt_gpt5nano.md (GPT-5 Nano结果)"
echo "   - compressed_langchain_result.md (LangChain结果)"
echo "   - compressed_custom_result.md (自定义方法结果)"
